import re
import time
import uuid
import async<PERSON>
from typing import Literal


from heracles.agent_roles.role_base import RoleBase
from heracles.core.schema import LLMAbilityType, ProjectErrorMessage, LimitedStack
from heracles.core.schema.models import (
    ErrorFoundModel,
)
from heracles.core.exceptions import AgentRunException
from .prompts import (
    ANALYZE_ERROR_LOG_PROMPT,
    ANALYZE_SCREENSHOT_PROMPT,
)
from heracles.core.utils.context_prompt_builder import PromptBuilder
from heracles.agent_roles.code_role.utils import precise_ratio
from heracles.core.utils import wait_for
from heracles.core.utils.llm_function import tool_call_to_argument_pair
from heracles.core.schema import FocusComponentType
from heracles.agent_roles.utils import llm_ability
from heracles.core.utils.string import remove_ansi_escape_sequences


class ObserverLog():
    type: Literal['terminal', 'browser_console']
    lines: LimitedStack
    last_line_timestamp: float

    def __init__(self, type, max_lines: int = 5000):
        self.type = type
        self.lines = LimitedStack(max_lines)
        self.last_line_timestamp = time.time()

class CheckRole(RoleBase):
    """ 检查工作成果质量
    """
    # 匹配错误信息的正则表达式
    REGEX_ERR_STRING = r'error|not found|unexpected|undefined|invalid|unable|fail|denied|timeout|fatal|critical|exception|exit|trouble|no such file'  # noqa:E501
    # 错误上下文行数
    ERROR_CONTEXT_LINES = 5

    def __init__(self, workspace):
        super().__init__(workspace)
        self.check_role_task_handle = None
        # 存放每个terminal对应日志的字典对象, key是terminal_id, value是log对象；还有 browser_console 的 log
        self.log_dict: dict[str, ObserverLog] = {}
        self.terminal_id_type = {}
        self.partial_report_str = ''
        self.error_report_id = None
        self.collect_lint_errors_task = None
        self.collect_browser_console_logs_task = None
        self._error_regex = re.compile(self.REGEX_ERR_STRING, re.IGNORECASE)
        # 为每个日志源存储防抖任务的字典
        self._error_debounce_tasks: dict[str, asyncio.Task] = {}

    def get_llm_ability(self):
        return LLMAbilityType.NORMAL

    async def run(self):
        pass

    async def analyze_browser_console_logs(self):
        if not self.workspace.playground.ide_server_client.http_ports:
            return
        try:
            browser_logs = await self.workspace.tools.browser_console_logs()
            if not self.log_dict.get('browser_console'):
                self.log_dict['browser_console'] = ObserverLog('browser_console')
            for log in browser_logs:
                await self._process_line(log, self.log_dict['browser_console'])
        except Exception as e:
            self.logger.warning(f"Failed to get browser logs, using empty list: {e}")

    async def collect_browser_console_logs(self):
        await self.workspace.trigger_general_loading(tool_name='AutoFix', status='Checking browser console logs')
        try:
            def is_http_ports_ready():
                return self.workspace.playground.ide_server_client.http_ports
            await wait_for(is_http_ports_ready, timeout=10, interval=1)

            url = await self.workspace.tools.local_url()
            if url:
                self.logger.warning(f'-> goto url: {url}')
                await self.workspace.tools.browser_goto(url)
            await self.analyze_browser_console_logs()
        except AgentRunException as e:
            self.logger.warning(f'-> browser goto failed: {e}')

    async def collect_lint_errors(self):
        await self.workspace.trigger_general_loading(tool_name='AutoFix', status='Checking if any lint errors')
        await self.workspace.tools.get_lint_errors()

    async def run_and_check_project(self):
        # FIXME: browser console 没有严重性分类，会导致误报
        await self.workspace.trigger_general_loading(tool_name='AutoFix', status='Running project and waiting for terminal logs')
        asyncio.create_task(self.workspace.tools.get_lint_errors())

        run_status = self.workspace.tools.run_status()
        if run_status != 'RUNNING':
            # FIXME: 这里需要优化，run_project 需要等待 prompt 可用
            await self.workspace.tools.run_project()
        else:
            await self.workspace.tools.stop_project()
            await self.workspace.tools.run_project()

        self.collect_lint_errors_task = asyncio.create_task(self.collect_lint_errors())
        self.collect_browser_console_logs_task = asyncio.create_task(self.collect_browser_console_logs())

        # FIXME: browser console 没有严重性分类，会导致误报
        await self.workspace.trigger_general_loading(tool_name='AutoFix', status='Checking terminal logs')

        wait_seconds = 0

        def clacky_log_has_output():
            nonlocal wait_seconds
            wait_seconds += 1
            for terminal_id, log in self.log_dict.items():
                if terminal_id != 'browser_console' and self.terminal_id_type.get(terminal_id) != 'goAgent':
                    continue
                if len(log.lines.items) > 0:
                    return True
            return False

        try:
            await wait_for(clacky_log_has_output, timeout=5, interval=1)
            self.logger.warning(f'-> clacky log has output (waited {wait_seconds} seconds), start analysis...')
        except AgentRunException:
            self.collect_lint_errors_task.cancel()
            self.collect_browser_console_logs_task.cancel()
            self.collect_lint_errors_task = None
            self.collect_browser_console_logs_task = None
            raise AgentRunException('No terminal log found, the project may not be running. Please try again later') from None

    async def on_terminal(self, raw_lines, terminal_id, terminal_type=None):
        """监控终端日志"""
        await self.workspace.playground.ide_server_client.following_focus_component(FocusComponentType.TERMINAL)
        if terminal_id not in self.log_dict:
            self.log_dict[terminal_id] = ObserverLog('terminal')
        self.terminal_id_type[terminal_id] = terminal_type
        await self._process_line(raw_lines, self.log_dict[terminal_id])

    async def _process_line(self, raw_lines, log: ObserverLog):
        raw_lines = (
            remove_ansi_escape_sequences(raw_lines)
            .removeprefix('PaasNewLineSign')
            .replace('\x03', '')
        )
        lines = raw_lines.split('\n')

        log.lines.append_to_last_one(lines[0])
        for line in lines[1:]:
            log.lines.push(line)

        if self.workspace.smart_detect.status == 'monitoring_errors':
            log.last_line_timestamp = time.time()
            if self._error_regex.search(raw_lines):
                await self.handle_error(log)

    async def analyze_screenshot(self):
        browser_screenshot = await self.workspace.tools.browser_screenshot()
        if not browser_screenshot:
            return
        error_result = await self.check_if_new_error(image_url=browser_screenshot)
        if error_result.is_new_error:
            error_message_obj = ProjectErrorMessage(
                title=error_result.title,
                ref_id=f"screenshot/{uuid.uuid4().hex[:8]}",
                category='screenshot',
                content=browser_screenshot
            )
            self.workspace.smart_detect.errors.append(error_message_obj)

    def _extract_error_context(self, log: ObserverLog) -> str:
        """提取错误上下文: 匹配到错误关键字的第一行和最后一行, 只取第一行前N行到最后一行后N行的内容"""
        lines = log.lines.get_items()
        if not lines:
            return ""

        # 一次遍历找到第一个和最后一个匹配行
        first_error_line = last_error_line = -1
        for i, line in enumerate(lines):
            if self._error_regex.search(line):
                if first_error_line is -1:
                    first_error_line = i
                last_error_line = i

        if first_error_line is -1:
            return ""

        # 直接计算并返回提取范围
        return "\n".join(lines[
            max(0, first_error_line - self.ERROR_CONTEXT_LINES):
            min(len(lines), last_error_line + self.ERROR_CONTEXT_LINES + 1)
        ])

    async def handle_error(self, log: ObserverLog):
        """处理错误，为每个日志源提供独立的防抖处理"""
        log_key = self._find_log_key(log)
        if log_key is None:
            self.logger.warning(f"Failed to find log key for log object {log}")
            return

        self._debounce_task(log_key, lambda: self._process_error_for_log(log, log_key))

    def _debounce_task(self, key: str, task_func, delay: float = 1.0):
        """通用的防抖任务处理"""
        # 取消之前的任务
        if old_task := self._error_debounce_tasks.get(key):
            old_task.cancel()

        # 创建新的防抖任务
        async def delayed_task():
            try:
                await asyncio.sleep(delay)
                await task_func()
            except asyncio.CancelledError:
                pass  # 任务被取消，正常情况
            except Exception as e:
                await self.trigger_error_handler(f"Error handler for {key} failed: {e}")
            finally:
                self._error_debounce_tasks.pop(key, None)

        self._error_debounce_tasks[key] = asyncio.create_task(delayed_task())

    def _find_log_key(self, log: ObserverLog) -> str | None:
        """查找日志对象对应的key"""
        return next((key for key, log_obj in self.log_dict.items() if log_obj is log), None)



    async def _process_error_for_log(self, log: ObserverLog, log_key: str):
        """处理特定日志源的错误"""
        error_result = await self.check_if_new_error(log)
        if not error_result.is_new_error:
            return
        self.logger.info(f'-> LLM handle_error (_error_task_{log_key.replace("-", "_")}): {error_result.title}')
        error_message_obj = ProjectErrorMessage(
            title=error_result.title,
            ref_id=f"{log.type}/{uuid.uuid4().hex[:8]}",
            category=log.type,
            content=self._extract_error_context(log)
        )
        self.workspace.smart_detect.errors.append(error_message_obj)
        if not self.workspace.auto_fix.is_working():
            await self.workspace.trigger('message_suggestion', error_message_obj.generate_suggestion())
        log.lines.clear()

    @llm_ability(LLMAbilityType.FAST)
    async def check_if_new_error(self, log: ObserverLog | None = None, image_url: str | None = None):
        if log:
            message_builder = PromptBuilder(ANALYZE_ERROR_LOG_PROMPT, self.workspace)
            content = self._extract_error_context(log)
            for error in self.workspace.smart_detect.get_errors(category=log.type):
                similarity = precise_ratio(content, error.content)
                if similarity > 0.8:
                    self.logger.warning(f"new error is duplicate with history error: {error.title}")
                    return ErrorFoundModel(is_new_error=False, title=error.title)
            message_builder.errors = self.workspace.smart_detect.errors
            message = await message_builder.format(content=content)
        else:
            message_builder = PromptBuilder(ANALYZE_SCREENSHOT_PROMPT, self.workspace)
            message = await message_builder.format(content="current screenshot of the root page as provided")

        if image_url:
            result = await self.aask(message, images=[image_url], response_model=ErrorFoundModel)
        else:
            result = await self.aask(message, response_model=ErrorFoundModel)
        return result

    async def trigger_error_handler(self, message):
        await self.workspace.playground.trigger_error_handler(message)

    async def trigger_tool_callback(self, tool_call, status):
        try:
            if status == 'end':
                tool_name, value = tool_call_to_argument_pair(tool_call)
                await self.workspace.trigger('chunk_message', f"\n> {tool_name}: `{value}`\n")
        except Exception as e:
            self.logger.error(f"Error in trigger_tool_callback: {e}, tool_call: {tool_call}")

    async def trigger_chunk_message(self, word: str):
        await self.workspace.trigger('chunk_message', word)
